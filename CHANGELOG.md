## [0.1.4](https://github.com/ClashStrategic/stats/compare/v0.1.3...v0.1.4) (2025-07-06)


### Bug Fixes

* **DeploymentTime:** remove inconsistent DeploymentTime attribute ([e5f6837](https://github.com/ClashStrategic/stats/commit/e5f68376e30696c46ba50f24b819742986bd42ea))

## [0.1.3](https://github.com/ClashStrategic/stats/compare/v0.1.2...v0.1.3) (2025-07-06)


### Bug Fixes

* **data:** normalize boolean false to null for card attributes ([478b6ca](https://github.com/ClashStrategic/stats/commit/478b6ca91317babb73ac575f5866839d1dc93eef))
* **radio:** Removes the inconsistent "radio" attribute from the "Royal Giant" card. ([66b772d](https://github.com/ClashStrategic/stats/commit/66b772d9579d5a99740878b4b87a1c809434a48d))
* **range:** normalize range attribute to null for melee cards ([ef2be60](https://github.com/ClashStrategic/stats/commit/ef2be604742bb757977cae0097531aa97a55eb2b))
* **TypeAttack:** resolve inconsistent TypeAttack boolean values ([657d2ed](https://github.com/ClashStrategic/stats/commit/657d2edea0944b22126be53f8dff36cf67dc13c9))

## [0.1.2](https://github.com/ClashStrategic/stats/compare/v0.1.1...v0.1.2) (2025-07-03)


### Bug Fixes

* **cards:** Removes unprepared special data of the normal and evolution types from the charts. ([75a0c94](https://github.com/ClashStrategic/stats/commit/75a0c940d02f5f3a0dde77f949449c5cbeaec529))

## [0.1.1](https://github.com/ClashStrategic/stats/compare/v0.1.0...v0.1.1) (2025-07-03)


### Bug Fixes

* **cards:** Integrates the missing "ChargeDamage" data into the cards with their false value. ([b03e46a](https://github.com/ClashStrategic/stats/commit/b03e46a7dba5e9771b25e01effbd50e32c2a5368))
